"""
Debug script to analyze OpenF1 API response structure and fix pandas DataFrame creation issues.

This script helps identify why the pandas DataFrame creation is failing and provides
a robust solution for handling different API response formats.
"""

import requests
import json
import pandas as pd
from typing import Union, List, Dict, Any


def debug_api_response(url: str) -> Dict[str, Any]:
    """
    Debug function to analyze the structure of OpenF1 API responses.
    
    This function helps identify why pandas DataFrame creation might be failing
    by examining the actual response structure and data types.
    
    Args:
        url (str): The OpenF1 API URL to test
        
    Returns:
        Dict[str, Any]: Analysis of the API response including structure and recommendations
    """
    try:
        # Make the API request
        print(f"🔍 Testing API URL: {url}")
        response = requests.get(url)
        response.raise_for_status()
        
        # Parse JSON response
        json_data = response.json()
        
        # Analyze response structure
        analysis = {
            "status": "success",
            "response_type": type(json_data).__name__,
            "response_length": len(json_data) if hasattr(json_data, '__len__') else "N/A",
            "sample_data": None,
            "recommendations": []
        }
        
        print(f"📊 Response Type: {analysis['response_type']}")
        print(f"📏 Response Length: {analysis['response_length']}")
        
        # Handle different response types
        if isinstance(json_data, list):
            if len(json_data) > 0:
                analysis["sample_data"] = json_data[0] if len(json_data) > 0 else None
                analysis["recommendations"].append("✅ List format - should work with pd.DataFrame()")
                print(f"📝 Sample Record: {json.dumps(json_data[0], indent=2)[:200]}...")
            else:
                analysis["recommendations"].append("⚠️ Empty list - no data available")
                print("📝 Empty response - no data available")
                
        elif isinstance(json_data, dict):
            analysis["sample_data"] = json_data
            # Check if it's a single record or has nested structure
            if all(isinstance(v, (str, int, float, bool, type(None))) for v in json_data.values()):
                analysis["recommendations"].append("⚠️ Single record dict - wrap in list: [json_data]")
            else:
                analysis["recommendations"].append("🔍 Complex dict structure - may need special handling")
            print(f"📝 Dict Structure: {json.dumps(json_data, indent=2)[:300]}...")
            
        else:
            analysis["recommendations"].append("❌ Unexpected response type - manual handling required")
            print(f"📝 Raw Response: {str(json_data)[:200]}...")
            
        return analysis
        
    except requests.exceptions.RequestException as e:
        return {
            "status": "error",
            "error_type": "request_error",
            "error_message": str(e),
            "recommendations": ["🔧 Check URL and network connectivity"]
        }
    except json.JSONDecodeError as e:
        return {
            "status": "error", 
            "error_type": "json_error",
            "error_message": str(e),
            "recommendations": ["🔧 Response is not valid JSON"]
        }
    except Exception as e:
        return {
            "status": "error",
            "error_type": "unknown_error", 
            "error_message": str(e),
            "recommendations": ["🔧 Unexpected error occurred"]
        }


def robust_data_getter(url: str) -> pd.DataFrame:
    """
    Robust version of data_getter that handles different API response formats.
    
    This function properly handles various OpenF1 API response structures and
    creates pandas DataFrames without the scalar values error.
    
    Args:
        url (str): OpenF1 API URL
        
    Returns:
        pd.DataFrame: Properly formatted DataFrame or empty DataFrame if no data
        
    Raises:
        Exception: If API request fails or data is completely invalid
    """
    try:
        print(f"🚀 Fetching data from: {url}")
        
        # Make API request
        response = requests.get(url)
        response.raise_for_status()
        
        # Parse JSON
        json_data = response.json()
        print(f"📦 Received {type(json_data).__name__} with length: {len(json_data) if hasattr(json_data, '__len__') else 'N/A'}")
        
        # Handle different response types
        if isinstance(json_data, list):
            if len(json_data) == 0:
                print("⚠️ Empty response - returning empty DataFrame")
                return pd.DataFrame()
            else:
                print(f"✅ Converting {len(json_data)} records to DataFrame")
                return pd.DataFrame(json_data)
                
        elif isinstance(json_data, dict):
            # Check if it's a single record (all values are scalars)
            if all(isinstance(v, (str, int, float, bool, type(None))) for v in json_data.values()):
                print("🔧 Single record detected - wrapping in list")
                return pd.DataFrame([json_data])  # Wrap single record in list
            else:
                print("🔍 Complex dict structure - attempting direct conversion")
                try:
                    return pd.DataFrame(json_data)
                except ValueError as e:
                    print(f"❌ Direct conversion failed: {e}")
                    print("🔧 Attempting to normalize nested structure")
                    return pd.json_normalize(json_data)
                    
        else:
            raise ValueError(f"Unexpected response type: {type(json_data)}")
            
    except requests.exceptions.RequestException as e:
        raise Exception(f"API request failed: {e}")
    except json.JSONDecodeError as e:
        raise Exception(f"Invalid JSON response: {e}")
    except Exception as e:
        raise Exception(f"Data processing failed: {e}")


# Test the problematic URL
if __name__ == "__main__":
    test_url = "https://api.openf1.org/v1/car_data?session_key=9462&meeting_key=1228"
    
    print("=" * 80)
    print("🔍 DEBUGGING OPENF1 API RESPONSE")
    print("=" * 80)
    
    # First, debug the response structure
    analysis = debug_api_response(test_url)
    
    print("\n📋 ANALYSIS RESULTS:")
    print("-" * 40)
    for key, value in analysis.items():
        if key != "sample_data":
            print(f"{key}: {value}")
    
    print("\n💡 RECOMMENDATIONS:")
    print("-" * 40)
    for rec in analysis.get("recommendations", []):
        print(f"  {rec}")
    
    # If successful, try the robust data getter
    if analysis["status"] == "success":
        print("\n" + "=" * 80)
        print("🚀 TESTING ROBUST DATA GETTER")
        print("=" * 80)
        
        try:
            df = robust_data_getter(test_url)
            print(f"\n✅ SUCCESS! Created DataFrame with shape: {df.shape}")
            if not df.empty:
                print(f"📊 Columns: {list(df.columns)}")
                print(f"📝 Sample data:\n{df.head()}")
            else:
                print("📝 DataFrame is empty (no data available)")
                
        except Exception as e:
            print(f"\n❌ FAILED: {e}")
            
    print("\n" + "=" * 80)
