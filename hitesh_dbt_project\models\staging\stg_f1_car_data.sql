/*
  Staging Model: F1 Car Telemetry Data

  Purpose:
  This staging model cleans and standardizes raw Formula 1 car telemetry data from the OpenF1 API.
  It serves as the foundation for all downstream telemetry analysis including performance metrics,
  driver behavior analysis, and real-time race monitoring.

  Data Source:
  - Raw table: F1_CAR_DATA (loaded via Airflow ETL pipeline)
  - Frequency: High-frequency telemetry data (multiple records per second during sessions)
  - Coverage: All F1 sessions (practice, qualifying, sprint, race)

  Transformations Applied:
  1. Data type standardization (driver_number as VARCHAR for consistency)
  2. Timestamp normalization to timezone-neutral format
  3. Basic data cleaning and validation

  Key Metrics Included:
  - RPM: Engine revolutions per minute (0-15000 range)
  - Speed: Car speed in km/h (0-350+ range)
  - Throttle: Throttle pedal position percentage (0-100)
  - Brake: Brake pedal pressure/position (0-100)
  - DRS: Drag Reduction System status (0=closed, 1=open)

  Usage:
  This model is used by intermediate models for:
  - Performance analysis (speed traces, acceleration profiles)
  - Driver behavior analysis (braking patterns, throttle application)
  - Race strategy analysis (DRS usage, energy management)
  - Real-time telemetry visualization
*/

SELECT
    session_key,                                    -- F1 session identifier for joining with other data
    driver_number::VARCHAR(50) as driver_number,   -- Driver number as string for consistent joins
    TO_TIMESTAMP_NTZ(date) as timestamp,           -- Normalized timestamp without timezone for analysis
    rpm,                                           -- Engine RPM for performance analysis
    speed,                                         -- Car speed in km/h for lap time analysis
    throttle,                                      -- Throttle position (0-100%) for driving style analysis
    brake,                                         -- Brake pressure/position for braking analysis
    drs                                           -- DRS status (0/1) for aerodynamic analysis
FROM {{ source('HITESH', 'F1_CAR_DATA') }}        -- Source table loaded by Airflow pipeline