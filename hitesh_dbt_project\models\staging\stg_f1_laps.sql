/*
  Staging Model: F1 Lap Times and Sector Performance

  Purpose:
  This staging model processes Formula 1 lap timing data, providing clean and standardized
  lap performance metrics for race analysis. It calculates lap end times and normalizes
  timing data for consistent downstream analysis.

  Data Source:
  - Raw table: F1_LAPS (loaded via Airflow ETL pipeline)
  - Frequency: One record per completed lap per driver
  - Coverage: All F1 sessions with lap timing data

  Key Transformations:
  1. Timestamp normalization (truncated to seconds for consistency)
  2. Calculated lap end time based on start time + duration
  3. Sector timing validation and standardization
  4. Pit out lap flagging for strategy analysis

  Business Logic:
  - Lap times are the fundamental unit of F1 performance measurement
  - Sector times allow detailed analysis of driver and car performance
  - Pit out laps are flagged as they typically have slower times due to cold tires
  - Date calculations enable time-based analysis and visualization

  Key Metrics:
  - lap_duration: Total lap time in seconds (primary performance metric)
  - duration_sector_*: Individual sector times for detailed analysis
  - is_pit_out_lap: Boolean flag for strategy and performance analysis

  Usage:
  This model feeds into:
  - Race pace analysis and comparisons
  - Qualifying performance evaluation
  - Tire degradation analysis
  - Race strategy optimization
  - Driver performance benchmarking
*/

WITH source AS (
    -- Source all lap timing data from the raw F1_LAPS table
    SELECT * FROM {{ source('HITESH', 'F1_LAPS') }}
)

SELECT
    session_key,                                                                    -- F1 session identifier for data joining
    driver_number,                                                                  -- Driver number for performance tracking
    DATE_TRUNC('second', date_start) AS date_start,                                -- Lap start time (normalized to seconds)
    DATEADD(second, lap_duration, DATE_TRUNC('second', date_start)) AS date_end,   -- Calculated lap end time for analysis
    lap_duration,                                                                   -- Total lap time in seconds (key performance metric)
    lap_number,                                                                     -- Sequential lap number within the session
    is_pit_out_lap,                                                                -- Flag indicating lap after pit stop (affects performance)
    duration_sector_1,                                                             -- Sector 1 time for detailed performance analysis
    duration_sector_2,                                                             -- Sector 2 time for cornering performance
    duration_sector_3                                                              -- Sector 3 time for straight-line speed analysis
FROM source