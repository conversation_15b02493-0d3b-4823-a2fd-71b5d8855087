/*
  Marts Model: F1 Race Session Summary

  Purpose:
  This marts model provides comprehensive session-level summary statistics for Formula 1
  races and practice sessions. It aggregates data from multiple sources to create a
  high-level overview of each session's key metrics and characteristics.

  Business Value:
  - Enables quick session comparison and analysis
  - Provides key performance indicators for race analysis
  - Supports dashboard creation and reporting
  - Facilitates historical trend analysis across seasons

  Key Metrics Included:
  - Participation metrics (total drivers, teams)
  - Session characteristics (total laps, pit stops)
  - Performance indicators (fastest speeds, averages)
  - Strategic elements (DRS usage, pit stop frequency)

  Data Sources:
  - Position data for driver participation
  - Lap data for session duration and completion
  - Leaderboard data for team statistics and points
  - Telemetry data for speed and technical metrics
  - Session mapping for human-readable names

  Usage:
  This model serves as the foundation for:
  - Executive dashboards and KPI reporting
  - Session comparison analysis
  - Historical performance tracking
  - Race weekend summary reports
*/

-- CTE to calculate driver participation metrics per session
WITH positions AS (
   SELECT
       session_key,                                                    -- Session identifier for grouping
       COUNT(DISTINCT driver_number) as total_drivers                  -- Count unique drivers who participated
       -- COUNT(DISTINCT CASE WHEN position > 20 THEN driver_number END) as retirements  -- Future: retirement tracking
   FROM {{ ref('stg_f1_position') }}                                   -- Source: cleaned position data
   GROUP BY session_key
),

-- CTE to determine session duration and completion metrics
laps AS (
   SELECT
       session_key,                                                    -- Session identifier
       MAX(lap_number) as total_laps                                   -- Maximum lap number indicates session length
   FROM {{ ref('stg_f1_laps') }}                                       -- Source: cleaned lap timing data
   GROUP BY session_key
),

-- CTE to aggregate team-level statistics and performance
team_stats AS (
   SELECT
       session_key,                                                    -- Session identifier
       team_name,                                                      -- Constructor/team name
       COUNT(DISTINCT driver_number) as cars_finished,                 -- Number of cars per team that finished
       SUM(points) as constructor_points,                              -- Total championship points earned
       SUM(pit_stop_count) as total_pit_stops                         -- Total pit stops for strategic analysis
   FROM {{ ref('f1_race_leaderboard') }}                               -- Source: race results and standings
   GROUP BY session_key, team_name
),

-- CTE to calculate driver performance and technical metrics
driver_stats AS (
   SELECT
       session_key,                                                    -- Session identifier
       driver_number,                                                  -- Driver identifier
       MAX(speed) as top_speed,                                        -- Highest speed achieved during session
       AVG(speed) as avg_speed,                                        -- Average speed for pace analysis
       SUM(CASE WHEN drs = 1 THEN 1 ELSE 0 END) as drs_activations   -- DRS usage count for strategic analysis
   FROM {{ ref('stg_f1_car_data') }}                                   -- Source: car telemetry data
   GROUP BY session_key, driver_number
)

-- Final aggregation to create session-level summary
SELECT
   p.session_key,                                                      -- Primary key: session identifier
   m.session_display_name,                                             -- Human-readable session name (e.g., "Race", "Qualifying")
   p.total_drivers,                                                    -- Number of drivers who participated
   --p.retirements,                                                    -- Future: number of retirements/DNFs
   l.total_laps,                                                       -- Total laps completed in session
   COUNT(DISTINCT t.team_name) as total_teams,                         -- Number of teams that participated
   SUM(t.total_pit_stops) as total_pit_stops,                         -- Total pit stops across all teams
   MAX(d.top_speed) as fastest_speed,                                  -- Fastest speed recorded in session
   AVG(d.avg_speed) as average_speed                                   -- Overall average speed across all drivers
   -- SUM(d.drs_activations) as total_drs_activations                 -- Future: total DRS activations
FROM positions p                                                       -- Base table: driver participation
JOIN laps l ON p.session_key = l.session_key                          -- Join: session duration data
JOIN {{ ref('f1_session_name_mapping') }} m ON p.session_key = m.session_key  -- Join: human-readable names
LEFT JOIN team_stats t ON p.session_key = t.session_key               -- Left join: team statistics (may not exist for all sessions)
LEFT JOIN driver_stats d ON p.session_key = d.session_key             -- Left join: driver performance metrics
GROUP BY p.session_key, m.session_display_name, p.total_drivers, l.total_laps  -- Group by session-level attributes