/*
  DBT Test Macro: F1 Speed Range Validation

  Purpose:
  This custom test macro validates that Formula 1 speed values fall within realistic ranges
  for F1 cars. It's designed to catch data quality issues in telemetry data that could
  indicate API errors, sensor malfunctions, or data transmission problems.

  Business Context:
  - F1 cars typically reach speeds between 0-350 km/h during normal racing
  - Maximum theoretical speed on fastest circuits can approach 370-380 km/h
  - The 410 km/h upper limit provides buffer for extreme cases while catching obvious errors
  - Negative speeds are physically impossible and indicate data corruption

  Usage:
  Apply this test to any speed-related columns in F1 telemetry data:
  - car_data.speed (primary use case)
  - sector speed traps
  - calculated speed metrics

  Parameters:
  - model: The DBT model/table to test
  - column_name: The specific speed column to validate

  Test Logic:
  - Identifies records where speed < 0 (impossible values)
  - Identifies records where speed > 410 km/h (unrealistic for F1)
  - Returns failing records for investigation
  - Test passes when no records are returned (all speeds in valid range)

  Example Usage in schema.yml:
  tests:
    - speed_range:
        column_name: speed
*/

{% test speed_range(model, column_name) %}

-- CTE to extract speed values for validation
with validation as (
    select
        {{ column_name }} as speed_value    -- Extract the speed column to be tested
    from {{ model }}                        -- From the specified model/table
    where {{ column_name }} is not null     -- Only test non-null values
),

-- CTE to identify speed values outside acceptable F1 range
validation_errors as (
    select
        speed_value
    from validation
    where speed_value < 0                   -- Catch impossible negative speeds
       or speed_value > 410                 -- Catch unrealistic high speeds (F1 max ~370 km/h)
)

-- Return any records that fail validation
-- Test passes when this query returns no rows
select *
from validation_errors

{% endtest %}