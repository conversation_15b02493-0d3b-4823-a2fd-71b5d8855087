"""
Test script to verify the correct OpenF1 API usage without meeting_key parameter.

This demonstrates the proper way to call the OpenF1 API using only session_key
and optional filtering parameters like driver_number.
"""

import requests
import pandas as pd
import json


def test_api_call(url):
    """Test an API call and show the response structure."""
    print(f"🔍 Testing: {url}")
    
    try:
        response = requests.get(url)
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Response type: {type(data).__name__}")
            print(f"📏 Data length: {len(data) if hasattr(data, '__len__') else 'N/A'}")
            
            if isinstance(data, list) and len(data) > 0:
                print(f"📝 Sample record keys: {list(data[0].keys())}")
                print(f"📝 First record: {json.dumps(data[0], indent=2)[:300]}...")
                
                # Try creating DataFrame
                df = pd.DataFrame(data)
                print(f"🎉 DataFrame created successfully! Shape: {df.shape}")
                print(f"📊 Columns: {list(df.columns)}")
                return df
            elif isinstance(data, list) and len(data) == 0:
                print("⚠️ Empty response - no data available for this session/driver")
                return pd.DataFrame()
            else:
                print(f"🔍 Unexpected data structure: {data}")
                return None
                
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return None


def test_different_sessions():
    """Test different session keys to find working examples."""
    print("=" * 80)
    print("🏎️ TESTING CORRECT OPENF1 API USAGE")
    print("=" * 80)
    
    # Test cases with known session keys (these are from 2023 season)
    test_cases = [
        {
            "name": "Bahrain GP 2023 - Practice 1",
            "url": "https://api.openf1.org/v1/car_data?session_key=9158&driver_number=55",
            "description": "Carlos Sainz car data from Bahrain GP Practice 1"
        },
        {
            "name": "Bahrain GP 2023 - Practice 2", 
            "url": "https://api.openf1.org/v1/car_data?session_key=9159&driver_number=55",
            "description": "Carlos Sainz car data from Bahrain GP Practice 2"
        },
        {
            "name": "Bahrain GP 2023 - Qualifying",
            "url": "https://api.openf1.org/v1/car_data?session_key=9161&driver_number=55",
            "description": "Carlos Sainz car data from Bahrain GP Qualifying"
        },
        {
            "name": "Bahrain GP 2023 - Race",
            "url": "https://api.openf1.org/v1/car_data?session_key=9165&driver_number=55",
            "description": "Carlos Sainz car data from Bahrain GP Race"
        }
    ]
    
    working_examples = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"   {test_case['description']}")
        print("-" * 60)
        
        df = test_api_call(test_case['url'])
        
        if df is not None and not df.empty:
            working_examples.append({
                'name': test_case['name'],
                'url': test_case['url'],
                'dataframe': df
            })
            print(f"📈 Sample data preview:\n{df.head(3)}")
        
        print()
    
    # Show summary
    print("=" * 80)
    print("📋 SUMMARY OF WORKING EXAMPLES")
    print("=" * 80)
    
    if working_examples:
        print(f"✅ Found {len(working_examples)} working API calls!")
        
        for example in working_examples:
            print(f"\n🎯 {example['name']}")
            print(f"   URL: {example['url']}")
            print(f"   Data shape: {example['dataframe'].shape}")
            print(f"   Columns: {list(example['dataframe'].columns)}")
        
        # Show the corrected code pattern
        print(f"\n💡 CORRECTED CODE PATTERN:")
        print("-" * 50)
        print("# ✅ CORRECT - Use only session_key (and optional filters)")
        print(f"cars_url = \"{working_examples[0]['url']}\"")
        print("response = requests.get(cars_url)")
        print("cars_data = pd.DataFrame(response.json())")
        print("cars_data.head()")
        print()
        print("# ❌ INCORRECT - Don't use meeting_key with session_key")
        print("# cars_url = \"https://api.openf1.org/v1/car_data?session_key=9159&meeting_key=1228\"")
        
    else:
        print("❌ No working examples found. The session keys might be outdated.")
        print("💡 Try finding current session keys from:")
        print("   https://api.openf1.org/v1/sessions?year=2024")


if __name__ == "__main__":
    test_different_sessions()
