"""
Formula 1 Data Pipeline - Airflow DAG

This module implements a comprehensive Formula 1 data ingestion and transformation pipeline
using Apache Airflow for orchestration. The pipeline follows the Write-Audit-Publish (WAP)
pattern to ensure data quality and idempotency.

Architecture Overview:
- Extracts data from OpenF1 API for race sessions, telemetry, and metadata
- Loads raw data into Snowflake data warehouse using Snowpark
- Transforms data using DBT for analytical models
- Supports both real-time streaming and historical batch processing
- Implements intelligent backfill with session-day filtering to optimize compute costs

Key Features:
- Automatic retry mechanisms with exponential backoff
- Idempotent data loading using merge operations
- Dynamic task generation based on available race sessions
- Comprehensive error handling and logging
- Integration with DBT for data transformations
- Support for multiple F1 data endpoints (car_data, laps, weather, etc.)

Author: <PERSON><PERSON>
Created: 2024
"""

# Core Airflow imports for DAG definition and task decorators
from airflow.decorators import dag, task
from airflow.utils.dates import datetime
from datetime import timedelta, timezone
import logging
from typing import Dict, List, Any

# Custom F1 data ingestion class for API interactions and Snowflake operations
from include.hitesh.scripts.f1_snowflake_etl_2 import F1DataIngestion
# Snowflake session management utility
from include.eczachly.snowflake_queries import get_snowpark_session

# Environment and configuration management
import os
from dotenv import load_dotenv

# DBT integration for data transformations using Cosmos
from cosmos import DbtTaskGroup, ProjectConfig, ProfileConfig, RenderConfig
# Empty operator for workflow control
from airflow.operators.empty import EmptyOperator



# ============================================================================
# CONFIGURATION SECTION
# ============================================================================

# Load DBT environment variables from the project-specific .env file
# This ensures all Snowflake connection parameters are available for DBT operations
dbt_env_path = os.path.join(os.environ['AIRFLOW_HOME'], 'hitesh_dbt_project', 'dbt.env')
load_dotenv(dbt_env_path)

# DBT paths configuration
# Define absolute paths to DBT project components for consistent access across tasks
airflow_home = os.getenv('AIRFLOW_HOME')  # Get Airflow home directory from environment
PATH_TO_DBT_PROJECT = f'{airflow_home}/hitesh_dbt_project'  # Root directory of DBT project
PATH_TO_DBT_PROFILES = f'{airflow_home}/hitesh_dbt_project/profiles.yml'  # Snowflake connection config

# DBT profile configuration for Cosmos integration
# This configures how DBT connects to Snowflake and which environment to target
profile_config = ProfileConfig(
    profile_name="formula_one",  # Must match the profile name in profiles.yml
    target_name="dev",           # Environment target (dev/staging/prod)
    profiles_yml_filepath=PATH_TO_DBT_PROFILES,  # Path to connection configuration
)

# ============================================================================
# UTILITY FUNCTIONS FOR F1 DATA RETRIEVAL
# ============================================================================

def get_f1_calendar(year: int) -> List[Dict]:
    """
    Retrieve all Formula 1 race meetings for a specified year.

    This function fetches the complete F1 calendar including all Grand Prix events,
    sprint races, and testing sessions. The data is sourced from the OpenF1 API
    and includes meeting metadata such as dates, locations, and circuit information.

    Args:
        year (int): The F1 season year to retrieve (e.g., 2024)

    Returns:
        List[Dict]: A list of meeting dictionaries sorted by date_start, each containing:
            - meeting_key: Unique identifier for the meeting
            - year: Season year
            - country_name: Host country
            - circuit_short_name: Circuit abbreviation
            - location: Full location name
            - date_start: Meeting start date (ISO format)
            - date_end: Meeting end date (ISO format)

    Raises:
        Exception: Logs error if API request fails or data is malformed

    Example:
        >>> meetings = get_f1_calendar(2024)
        >>> print(f"Found {len(meetings)} meetings for 2024")
    """
    try:
        # Establish Snowpark session for potential data operations
        session = get_snowpark_session()
        # Initialize F1 data ingestion class with API capabilities
        ingestion = F1DataIngestion(session)

        # Prepare API parameters for year-specific meeting data
        year_params = {'year': year}
        # Make API request to OpenF1 meetings endpoint
        meetings = ingestion._make_request('meetings', year_params)

        if meetings:
            # Sort meetings chronologically by start date for logical processing order
            meetings.sort(key=lambda x: x['date_start'])
            return meetings
        return []  # Return empty list if no meetings found
    except Exception as e:
        logging.error(f"Error getting F1 calendar for year {year}: {str(e)}")
        return []


def get_all_sessions(year: int) -> List[Dict[str, List]]:
    """
    Retrieve all F1 session data for a specified year, formatted for DAG scheduling.

    This function fetches all practice, qualifying, sprint, and race sessions for the year
    and transforms them into a format suitable for Airflow DAG dynamic task generation.
    Each session is mapped to its date for efficient scheduling and backfill operations.

    Args:
        year (int): The F1 season year to retrieve sessions for

    Returns:
        List[Dict[str, List]]: A list of dictionaries where each dict maps:
            - Key: Session date in 'YYYY-MM-DD' format
            - Value: List containing [meeting_key, session_key] for that date

    Example:
        >>> sessions = get_all_sessions(2024)
        >>> # Returns: [{'2024-03-02': [1217, 9158]}, {'2024-03-03': [1217, 9159]}, ...]

    Note:
        - Sessions are sorted chronologically by start date
        - Date format conversion handles timezone information (Z suffix)
        - Used by DAG to determine which dates have F1 sessions for intelligent scheduling
    """
    try:
        # Establish Snowpark session for data operations
        session = get_snowpark_session()
        # Initialize F1 data ingestion class
        ingestion = F1DataIngestion(session)

        # Prepare API parameters for year-specific session data
        year_params = {'year': year}
        # Fetch all sessions for the specified year from OpenF1 API
        sessions = ingestion._make_request('sessions', year_params)

        if sessions:
            # Sort sessions chronologically for consistent processing order
            sessions.sort(key=lambda x: x['date_start'])
            # Transform session data into date-keyed format for DAG scheduling
            # Convert ISO datetime to date string and map to [meeting_key, session_key]
            return [
                {datetime.fromisoformat(m['date_start'].replace('Z', '+00:00')).strftime('%Y-%m-%d'):
                     [m['meeting_key'], m['session_key']]}
                for m in sessions
            ]
        return []  # Return empty list if no sessions found
    except Exception as e:
        logging.error(f"Error getting session dates for year {year}: {str(e)}")
        return []


# ============================================================================
# DAG INITIALIZATION AND CONFIGURATION
# ============================================================================

# Retrieve F1 calendar for 2024 season to determine the optimal DAG start date
# This ensures the DAG only runs during the actual F1 season period
f1_calendar_2024 = get_f1_calendar(2024)
first_race_date = None
if f1_calendar_2024:
    # Extract the first race date and convert from ISO format to datetime
    first_race_date = datetime.fromisoformat(f1_calendar_2024[0]['date_start'].replace('Z', '+00:00'))
    logging.info(f"First race of 2024 is on {first_race_date}")

# Pre-compute all session dates for 2024 to enable intelligent scheduling
# This allows the DAG to skip non-race days and optimize compute resource usage
session_dates_2024 = get_all_sessions(2024)
# Create a sorted list of unique session dates for efficient lookup
session_dates = sorted(list({
    date for date_dict in session_dates_2024
    for date in date_dict.keys()
}))


@dag(
    # Unique identifier for this DAG in the Airflow UI
    dag_id='f1_data_pipeline_backfill',
    # Human-readable description shown in Airflow UI
    description="F1 Data Ingestion Pipeline - Session Days Only",

    # Default arguments applied to all tasks in this DAG
    default_args={
        "owner": "Hitesh Kaushik",                    # DAG owner for notifications and permissions
        "retries": 3,                                 # Number of automatic retries on task failure
        "retry_delay": timedelta(minutes=2),          # Wait time between retry attempts
        "depends_on_past": False,                     # Tasks don't depend on previous DAG run success
        "execution_timeout": timedelta(hours=1),      # Maximum runtime before task is killed
        "email_on_retry": True,                       # Send email notifications on retries
        "max_active_tasks": 3,                        # Limit concurrent tasks to prevent resource overload
        "start_date": datetime(2024, 12, 7)           # DAG start date for backfill operations
    },

    # DAG-level configuration
    max_active_runs=1,          # Only one DAG instance can run at a time (prevents data conflicts)
    schedule_interval="@daily",  # Run once per day to check for new F1 session data
    catchup=True,               # Enable backfill for historical dates when DAG is first deployed
    tags=["f1", "snowpark", "etl", "racing"]  # Tags for organization and filtering in Airflow UI
)
def f1_data_pipeline_backfill():
    """
    Main DAG function defining the F1 data pipeline workflow.

    This DAG implements a comprehensive ETL pipeline for Formula 1 data with the following features:
    - Intelligent session-day filtering to optimize compute costs
    - Idempotent data loading using merge operations
    - Parallel processing of different data endpoints
    - Comprehensive error handling and retry mechanisms
    - Integration with DBT for data transformations

    Workflow Overview:
    1. Initialize and check if current date has F1 sessions
    2. Extract meeting and session metadata
    3. Process telemetry data (car_data, laps, weather, etc.) in parallel
    4. Process race-specific data (positions, intervals) sequentially
    5. Process technical data (pit stops, tire stints, race control)
    6. Clean up staging tables
    7. Execute DBT transformations for analytical models
    """

    # ========================================================================
    # TASK DEFINITIONS
    # ========================================================================

    @task
    def initialize_ingestion(**context):
        """
        Initialize the data ingestion process and validate if current date has F1 sessions.

        This task implements intelligent scheduling by checking if the current execution date
        corresponds to an actual F1 session day. This optimization prevents unnecessary
        compute resource usage on non-race days while ensuring all race data is captured.

        Args:
            **context: Airflow context containing execution metadata

        Returns:
            dict or None: Session information if it's a race day, None otherwise
                - execution_date: ISO format date string
                - sessions: List of session metadata (meeting_key, session_key)
                - status: Processing status indicator
                - is_race_day: Boolean flag for downstream task logic

        Note:
            Returning None causes downstream tasks to be skipped via trigger rules,
            implementing the cost-saving "skip non-race days" feature.
        """
        # Extract the execution date from Airflow context
        execution_date = context['data_interval_start']
        logging.info(f"Processing date: {execution_date}")

        # Convert execution date to string format for comparison with session dates
        date_str = execution_date.strftime('%Y-%m-%d')
        logging.info(f"Checking if {date_str} is a session date")

        # Check if current date has any F1 sessions (practice, qualifying, race, etc.)
        if date_str not in session_dates:
            logging.info(f"Skipping non-session day {date_str} - COMPUTE COST OPTIMIZATION")
            return None  # This will skip all downstream tasks

        # Find all sessions scheduled for the current date
        matching_sessions = []
        for session_dict in session_dates_2024:
            if date_str in session_dict:
                # Extract meeting and session identifiers for API calls
                meeting_key = session_dict[date_str][0]  # Grand Prix weekend identifier
                session_key = session_dict[date_str][1]  # Specific session (FP1, FP2, Q1, Race, etc.)
                matching_sessions.append({
                    'meeting_key': meeting_key,
                    'session_key': session_key,
                    'execution_date': execution_date
                })

        # Validate that sessions were found (defensive programming)
        if not matching_sessions:
            logging.info(f"No sessions found for date {date_str}")
            return None

        logging.info(f"Found {len(matching_sessions)} sessions for {date_str}")
        # Return session metadata for downstream tasks
        return {
            'execution_date': execution_date.isoformat(),
            'sessions': matching_sessions,
            'status': 'initialized',
            'is_race_day': True  # Flag used by race-specific data processing tasks
        }

    @task
    def get_meeting_data(init_status: dict):
        """
        Extract and load F1 meeting (Grand Prix weekend) metadata.

        This task fetches comprehensive information about F1 race weekends including
        circuit details, dates, location, and event metadata. Meeting data serves as
        the foundation for all subsequent session and telemetry data processing.

        Args:
            init_status (dict): Initialization status from previous task containing
                              session information and execution metadata

        Returns:
            list or None: List of meeting dictionaries with metadata, None if no data

        Data Loaded:
            - meeting_key: Unique identifier for the Grand Prix weekend
            - year: F1 season year
            - country_name: Host country
            - circuit_short_name: Circuit abbreviation (e.g., 'MON', 'SPA')
            - location: Full location name
            - date_start/date_end: Weekend start and end dates

        Note:
            Implements idempotent loading - existing meetings are not reprocessed
        """
        # Skip processing if initialization failed (no race day)
        if not init_status:
            logging.info("No valid init status, skipping meeting data fetch")
            return None

        # Establish Snowflake connection and configure ingestion class
        session = get_snowpark_session()
        ingestion = F1DataIngestion(session)
        # Set execution date for audit trail in loaded data
        ingestion.execution_date = datetime.fromisoformat(init_status['execution_date']).date()

        meetings = []
        # Process each session's associated meeting data
        for session_info in init_status['sessions']:
            # Prepare API parameters for meeting-specific data
            params = {'meeting_key': session_info['meeting_key']}

            try:
                # Fetch meeting data from OpenF1 API
                data = ingestion._make_request('meetings', params)
                if data:
                    meeting = data[0]  # API returns list, take first element

                    # Implement idempotent loading - only process new meetings
                    if not ingestion.record_exists('F1_MEETINGS', meeting['meeting_key']):
                        # Create table if it doesn't exist (first run scenario)
                        ingestion.create_table('F1_MEETINGS', meeting)
                        # Load meeting data using merge strategy for data consistency
                        ingestion.load_data('F1_MEETINGS', [meeting])
                        logging.info(f"Loaded new meeting {meeting['meeting_key']}")
                    meetings.append(meeting)
            except Exception as e:
                logging.error(f"Error processing meeting {session_info['meeting_key']}: {str(e)}")

        return meetings if meetings else None

    @task
    def get_sessions(meetings: list):
        """
        Extract and load F1 session data for all meetings.

        This task processes individual F1 sessions within each Grand Prix weekend,
        including practice sessions (FP1, FP2, FP3), qualifying (Q1, Q2, Q3),
        sprint sessions, and race sessions. Session data provides the context
        for all telemetry and timing data.

        Args:
            meetings (list): List of meeting dictionaries from previous task

        Returns:
            list: List of all session dictionaries across all meetings

        Data Loaded:
            - session_key: Unique identifier for the session
            - meeting_key: Associated Grand Prix weekend
            - session_name: Human-readable name (e.g., 'Practice 1', 'Race')
            - session_type: Session category (Practice, Qualifying, Race, etc.)
            - date_start/date_end: Session start and end times
            - gmt_offset: Timezone offset for local time conversion

        Note:
            Each session becomes the basis for telemetry data collection
        """
        # Skip processing if no meeting data available
        if not meetings:
            logging.info("No meeting data provided, skipping sessions fetch")
            return []

        # Establish Snowflake connection
        session = get_snowpark_session()
        ingestion = F1DataIngestion(session)

        all_sessions = []
        # Process sessions for each Grand Prix weekend
        for meeting in meetings:
            try:
                # Fetch all sessions for this meeting from OpenF1 API
                sessions = ingestion.get_session_data(meeting['meeting_key'])
                if sessions:
                    # Create sessions table if it doesn't exist (first run scenario)
                    ingestion.create_table('F1_SESSIONS', sessions[0])

                    # Process each session with idempotent loading
                    for sess in sessions:
                        # Only load new sessions to prevent duplicates
                        if not ingestion.record_exists('F1_SESSIONS', meeting['meeting_key'], sess['session_key']):
                            # Load session data using merge strategy
                            ingestion.load_data('F1_SESSIONS', [sess])
                            logging.info(f"Loaded new session {sess['session_key']}")
                    all_sessions.extend(sessions)
            except Exception as e:
                logging.error(f"Error processing sessions for meeting {meeting['meeting_key']}: {str(e)}")

        return all_sessions

    @task(trigger_rule='none_failed')
    def process_endpoint(sessions_list: List[dict], init_data: dict, endpoint: str) -> Dict[str, Any]:
        """
        Process F1 telemetry and timing data for a specific endpoint.

        This task handles the extraction and loading of high-frequency F1 data including:
        - car_data: Real-time telemetry (speed, throttle, brake, DRS, etc.)
        - laps: Lap times, sector times, and performance metrics
        - drivers: Driver information and team assignments
        - weather: Track conditions, temperature, humidity
        - team_radio: Radio communications between drivers and teams

        The task implements parallel processing capabilities and is designed to handle
        large volumes of telemetry data efficiently using adaptive windowing for car_data.

        Args:
            sessions_list (List[dict]): List of session metadata from previous task
            init_data (dict): Initialization data containing execution context
            endpoint (str): Specific data endpoint to process (e.g., 'car_data', 'laps')

        Returns:
            Dict[str, Any]: Processing results including:
                - endpoint: The processed endpoint name
                - processed: Boolean success indicator
                - execution_date: Date of processing
                - results: List of per-session processing results

        Note:
            Uses trigger_rule='none_failed' to continue processing even if some
            upstream tasks fail, maximizing data collection success rate.
        """
        # Handle case where initialization failed (no race day)
        if init_data is None:
            return {
                'endpoint': endpoint,
                'processed': False,
                'reason': 'no_init_data',
                'results': []
            }

        results = []
        # Establish Snowflake connection and configure ingestion
        session = get_snowpark_session()
        ingestion = F1DataIngestion(session)
        execution_date = datetime.fromisoformat(init_data['execution_date']).date()
        ingestion.execution_date = execution_date

        # Determine target table name based on endpoint
        table_name = f'F1_{endpoint.upper()}'

        # Process each session's data for this endpoint
        for session_info in sessions_list:
            session_key = session_info['session_key']
            meeting_key = session_info['meeting_key']

            try:
                # Implement idempotent processing - skip if data already exists
                if ingestion.record_exists(table_name, meeting_key, session_key):
                    results.append({
                        'session_key': session_key,
                        'status': 'skipped',
                        'reason': 'data_exists',
                        'records': 0
                    })
                    continue

                # Fetch data from OpenF1 API for this session and endpoint
                # For car_data, this uses adaptive windowing to handle high-frequency data
                data = ingestion.get_session_endpoint_data(session_info, endpoint)

                # Skip if no data available for this session/endpoint combination
                if not data:
                    results.append({
                        'session_key': session_key,
                        'status': 'skipped',
                        'reason': 'no_data',
                        'records': 0
                    })
                    continue

                # Create table if it doesn't exist (first run scenario)
                if not ingestion.table_exists(table_name):
                    ingestion.create_table(table_name, data[0])

                # Load data using merge strategy for consistency
                ingestion.load_data(table_name, data)

                results.append({
                    'session_key': session_key,
                    'status': 'success',
                    'records': len(data),
                    'meeting_key': meeting_key
                })

            except Exception as e:
                # Log error but continue processing other sessions
                results.append({
                    'session_key': session_key,
                    'status': 'error',
                    'reason': str(e),
                    'meeting_key': meeting_key
                })

        return {
            'endpoint': endpoint,
            'processed': True,
            'execution_date': execution_date.isoformat(),
            'results': results
        }

    @task(trigger_rule='none_failed')
    def process_race_data(sessions_list: List[dict], init_data: dict) -> Dict[str, Any]:
        """Process race-specific data (positions, intervals) sequentially"""
        if not init_data or not init_data.get('is_race_day'):
            return {
                'processed': False,
                'reason': 'not_race_day',
                'results': []
            }

        results = []
        session = get_snowpark_session()
        ingestion = F1DataIngestion(session)
        execution_date = datetime.fromisoformat(init_data['execution_date']).date()
        ingestion.execution_date = execution_date

        # Process intervals first, then positions
        for session_info in sessions_list:
            session_key = session_info['session_key']
            meeting_key = session_info['meeting_key']

            # Process intervals data
            try:
                if not ingestion.record_exists('F1_INTERVALS', meeting_key, session_key):
                    data = ingestion.get_intervals_data(session_key)
                    if data:
                        if not ingestion.table_exists('F1_INTERVALS'):
                            ingestion.create_table('F1_INTERVALS', data[0])
                        ingestion.load_data('F1_INTERVALS', data)
                        results.append({
                            'endpoint': 'intervals',
                            'session_key': session_key,
                            'status': 'success',
                            'records': len(data)
                        })
            except Exception as e:
                results.append({
                    'endpoint': 'intervals',
                    'session_key': session_key,
                    'status': 'error',
                    'error': str(e)
                })

            # Process position data only after intervals are done
            try:
                if not ingestion.record_exists('F1_POSITION', meeting_key, session_key):
                    data = ingestion.get_position_data(session_key)
                    if data:
                        if not ingestion.table_exists('F1_POSITION'):
                            ingestion.create_table('F1_POSITION', data[0])
                        ingestion.load_data('F1_POSITION', data)
                        results.append({
                            'endpoint': 'position',
                            'session_key': session_key,
                            'status': 'success',
                            'records': len(data)
                        })
            except Exception as e:
                results.append({
                    'endpoint': 'position',
                    'session_key': session_key,
                    'status': 'error',
                    'error': str(e)
                })

        return {
            'processed': True,
            'execution_date': execution_date.isoformat(),
            'results': results
        }


    @task(trigger_rule='none_failed')
    def process_technical_data(sessions_list: List[dict], init_data: dict) -> Dict[str, Any]:
        """Process technical data (stints, pit stops, race control)"""
        if not init_data:
            return {
                'processed': False,
                'reason': 'no_init_data',
                'results': []
            }

        results = []
        session = get_snowpark_session()
        ingestion = F1DataIngestion(session)
        execution_date = datetime.fromisoformat(init_data['execution_date']).date()
        ingestion.execution_date = execution_date

        technical_endpoints = ['stints', 'pit', 'race_control']

        for session_info in sessions_list:
            session_key = session_info['session_key']
            meeting_key = session_info['meeting_key']

            for endpoint in technical_endpoints:
                table_name = f'F1_{endpoint.upper()}'
                try:
                    if ingestion.record_exists(table_name, meeting_key, session_key):
                        continue

                    # Call appropriate method based on endpoint
                    if endpoint == 'stints':
                        data = ingestion.get_stints_data(session_key)
                    elif endpoint == 'pit':
                        data = ingestion.get_pit_data(session_key)
                    else:  # race_control
                        data = ingestion.get_race_control_data(session_key)

                    if data:
                        if not ingestion.table_exists(table_name):
                            ingestion.create_table(table_name, data[0])
                        ingestion.load_data(table_name, data)

                        results.append({
                            'endpoint': endpoint,
                            'session_key': session_key,
                            'status': 'success',
                            'records': len(data)
                        })

                except Exception as e:
                    results.append({
                        'endpoint': endpoint,
                        'session_key': session_key,
                        'status': 'error',
                        'error': str(e)
                    })

        return {
            'processed': True,
            'execution_date': execution_date.isoformat(),
            'results': results
        }

    @task(trigger_rule='all_done')
    def cleanup_staging():  # Changed name to avoid conflicts
        """Clean up any remaining staging tables"""
        try:
            session = get_snowpark_session()
            ingestion = F1DataIngestion(session)
            ingestion.cleanup_staging_tables()
            logging.info("Completed staging table cleanup")
        except Exception as e:
            logging.error(f"Error in staging table cleanup task: {str(e)}")

    pre_dbt_workflow = EmptyOperator(
        task_id="pre_dbt_workflow",
        trigger_rule="all_done"
    )

    # DBT transformation group
    dbt_transformations = DbtTaskGroup(
        group_id="f1_dbt_transformations",
        project_config=ProjectConfig(PATH_TO_DBT_PROJECT),
        profile_config=profile_config,
        render_config=RenderConfig(
            select=[
                "staging.stg_f1_*",  # All staging models
                "intermediate.int_*",  # All intermediate models
                "marts.f1_*"  # All final models
            ],
        ),
        operator_args={
            "retries": 2,
            "retry_delay": timedelta(minutes=2),
            "execution_timeout": timedelta(minutes=30)
        }
    )

    # Final checkpoint
    post_dbt_workflow = EmptyOperator(
        task_id="post_dbt_workflow",
        trigger_rule="all_done",
        retries = 2
    )





    # ========================================================================
    # WORKFLOW ORCHESTRATION AND TASK DEPENDENCIES
    # ========================================================================

    # Initialize the pipeline - this is the entry point that determines if processing should continue
    init_data = initialize_ingestion()  # Implements cost optimization by skipping non-race days

    # Sequential metadata extraction - these must complete before telemetry processing
    meeting_data = get_meeting_data(init_data)      # Extract Grand Prix weekend information
    sessions_data = get_sessions(meeting_data)      # Extract individual session details

    # Parallel processing of high-frequency telemetry endpoints
    # These can run simultaneously to maximize throughput and minimize total runtime
    endpoints = ['drivers', 'car_data', 'laps', 'team_radio', 'weather']
    endpoint_tasks = []

    # Dynamically create tasks for each endpoint to enable parallel processing
    for endpoint_name in endpoints:
        task_id = f'process_{endpoint_name}'
        # Override task_id to create unique task instances for each endpoint
        current_task = process_endpoint.override(task_id=task_id)(
            sessions_data,
            init_data,
            endpoint_name
        )
        endpoint_tasks.append(current_task)

    # Specialized data processing tasks with specific business logic
    race_data = process_race_data(sessions_data, init_data)        # Position and interval data
    technical_data = process_technical_data(sessions_data, init_data)  # Pit stops, tire stints, race control
    cleanup = cleanup_staging()  # Remove temporary staging tables to free storage

    # ========================================================================
    # DEPENDENCY CHAIN DEFINITION
    # ========================================================================

    # Sequential foundation: metadata must be extracted before telemetry processing
    init_data >> meeting_data >> sessions_data

    # Parallel telemetry processing: all endpoint tasks depend on sessions_data and feed into cleanup
    for endpoint_task in endpoint_tasks:
        sessions_data >> endpoint_task >> cleanup

    # Specialized data processing: runs in parallel with endpoint tasks
    sessions_data >> race_data >> cleanup
    sessions_data >> technical_data >> cleanup

    # DBT transformation workflow: executes after all raw data is loaded and staging is cleaned
    # This ensures data transformations work with complete, consistent datasets
    cleanup >> pre_dbt_workflow >> dbt_transformations >> post_dbt_workflow


# ============================================================================
# DAG INSTANTIATION
# ============================================================================

# Create the DAG instance - this registers the DAG with Airflow for scheduling
dag = f1_data_pipeline_backfill()