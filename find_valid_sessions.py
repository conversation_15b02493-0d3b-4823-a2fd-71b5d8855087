"""
<PERSON><PERSON>t to find valid F1 session and meeting keys for testing OpenF1 API calls.

This script helps you discover valid session_key and meeting_key combinations
that actually have data available in the OpenF1 API.
"""

import requests
import json
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>


def get_recent_meetings(year=2024):
    """Get recent F1 meetings for the specified year."""
    try:
        url = f"https://api.openf1.org/v1/meetings?year={year}"
        print(f"🔍 Fetching meetings for {year}...")
        
        response = requests.get(url)
        response.raise_for_status()
        
        meetings = response.json()
        if meetings:
            # Sort by date and get recent ones
            meetings.sort(key=lambda x: x['date_start'], reverse=True)
            print(f"✅ Found {len(meetings)} meetings for {year}")
            return meetings[:5]  # Return 5 most recent
        else:
            print(f"⚠️ No meetings found for {year}")
            return []
            
    except Exception as e:
        print(f"❌ Error fetching meetings: {e}")
        return []


def get_sessions_for_meeting(meeting_key):
    """Get all sessions for a specific meeting."""
    try:
        url = f"https://api.openf1.org/v1/sessions?meeting_key={meeting_key}"
        print(f"🔍 Fetching sessions for meeting {meeting_key}...")
        
        response = requests.get(url)
        response.raise_for_status()
        
        sessions = response.json()
        if sessions:
            print(f"✅ Found {len(sessions)} sessions for meeting {meeting_key}")
            return sessions
        else:
            print(f"⚠️ No sessions found for meeting {meeting_key}")
            return []
            
    except Exception as e:
        print(f"❌ Error fetching sessions for meeting {meeting_key}: {e}")
        return []


def test_car_data_availability(session_key, meeting_key):
    """Test if car data is available for a session."""
    try:
        # Try with a small time window first
        url = f"https://api.openf1.org/v1/car_data?session_key={session_key}&meeting_key={meeting_key}"
        
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            return len(data) if isinstance(data, list) else (1 if data else 0)
        else:
            return 0
            
    except Exception as e:
        return 0


def find_valid_sessions():
    """Find valid session and meeting key combinations with available car data."""
    print("=" * 80)
    print("🏎️ FINDING VALID F1 SESSIONS WITH CAR DATA")
    print("=" * 80)
    
    # Try both 2024 and 2023 to find data
    for year in [2024, 2023]:
        print(f"\n📅 Checking {year} season...")
        meetings = get_recent_meetings(year)
        
        valid_sessions = []
        
        for meeting in meetings:
            meeting_key = meeting['meeting_key']
            meeting_name = meeting.get('meeting_name', 'Unknown')
            country = meeting.get('country_name', 'Unknown')
            
            print(f"\n🏁 {meeting_name} ({country}) - Meeting Key: {meeting_key}")
            
            sessions = get_sessions_for_meeting(meeting_key)
            
            for session in sessions:
                session_key = session['session_key']
                session_name = session.get('session_name', 'Unknown')
                session_type = session.get('session_type', 'Unknown')
                
                # Test if car data is available
                car_data_count = test_car_data_availability(session_key, meeting_key)
                
                status = "✅ HAS DATA" if car_data_count > 0 else "❌ NO DATA"
                print(f"  📊 {session_name} ({session_type}) - Session Key: {session_key} - {status}")
                
                if car_data_count > 0:
                    valid_sessions.append({
                        'year': year,
                        'meeting_key': meeting_key,
                        'meeting_name': meeting_name,
                        'country': country,
                        'session_key': session_key,
                        'session_name': session_name,
                        'session_type': session_type,
                        'car_data_records': car_data_count,
                        'test_url': f"https://api.openf1.org/v1/car_data?session_key={session_key}&meeting_key={meeting_key}"
                    })
        
        if valid_sessions:
            print(f"\n🎉 Found {len(valid_sessions)} valid sessions with car data in {year}!")
            
            print("\n📋 VALID SESSION SUMMARY:")
            print("-" * 60)
            for i, session in enumerate(valid_sessions[:3], 1):  # Show top 3
                print(f"{i}. {session['meeting_name']} - {session['session_name']}")
                print(f"   Meeting Key: {session['meeting_key']}, Session Key: {session['session_key']}")
                print(f"   Records: {session['car_data_records']}")
                print(f"   Test URL: {session['test_url']}")
                print()
            
            return valid_sessions
    
    print("\n❌ No valid sessions found with car data!")
    return []


def test_working_example():
    """Test a working example with valid session keys."""
    print("\n" + "=" * 80)
    print("🧪 TESTING WORKING EXAMPLES")
    print("=" * 80)
    
    valid_sessions = find_valid_sessions()
    
    if valid_sessions:
        # Test the first valid session
        test_session = valid_sessions[0]
        test_url = test_session['test_url']
        
        print(f"\n🚀 Testing with: {test_session['meeting_name']} - {test_session['session_name']}")
        print(f"🔗 URL: {test_url}")
        
        try:
            response = requests.get(test_url)
            response.raise_for_status()
            
            data = response.json()
            
            if isinstance(data, list) and len(data) > 0:
                df = pd.DataFrame(data)
                print(f"\n✅ SUCCESS! Created DataFrame with shape: {df.shape}")
                print(f"📊 Columns: {list(df.columns)}")
                print(f"📝 Sample data:\n{df.head()}")
                
                # Show the corrected code
                print(f"\n💡 WORKING CODE FOR YOUR NOTEBOOK:")
                print("-" * 50)
                print(f'cars_url = "{test_url}"')
                print('response = requests.get(cars_url)')
                print('cars_data = pd.DataFrame(response.json())')
                print('cars_data.head()')
                
            else:
                print("⚠️ Empty or invalid response")
                
        except Exception as e:
            print(f"❌ Test failed: {e}")
    
    else:
        print("❌ No valid sessions available for testing")


if __name__ == "__main__":
    test_working_example()
