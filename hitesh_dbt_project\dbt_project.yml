# ============================================================================
# DBT Project Configuration for Formula 1 Data Analysis
# ============================================================================
#
# This configuration file defines the structure and behavior of the F1 data
# transformation pipeline using DBT (Data Build Tool). It orchestrates the
# transformation of raw F1 telemetry and timing data into analytical models
# suitable for race analysis, performance evaluation, and strategic insights.
#
# Project Overview:
# - Processes real-time and historical F1 data from OpenF1 API
# - Implements layered data architecture (staging -> intermediate -> marts)
# - Provides data quality testing and validation
# - Supports both batch and real-time analytics use cases
#
# Architecture Layers:
# 1. Staging: Clean and standardize raw data from Snowflake tables
# 2. Intermediate: Apply business logic and create reusable components
# 3. Marts: Final analytical models for dashboards and reporting
#
# Author: <PERSON><PERSON>
# Created: 2024
# ============================================================================

# Project identification and metadata
name: 'F1_Data_Analysis'                    # Project name used in model references and documentation

# DBT configuration version (required for compatibility)
config-version: 2                          # Use DBT config version 2 for modern features
version: '0.1'                             # Project version for tracking and deployment

# Snowflake connection profile (defined in profiles.yml)
profile: 'formula_one'                     # Profile name for database connection configuration

# Directory structure configuration
# These paths define where DBT looks for different types of files
model-paths: ["models"]                    # SQL models for data transformations
seed-paths: ["seeds"]                      # CSV files for reference data (drivers, teams, circuits)
test-paths: ["tests", "data-tests"]        # Custom data quality tests and validations
analysis-paths: ["analysis"]               # Ad-hoc analysis queries and exploration
macro-paths: ["macros"]                    # Reusable SQL functions and custom tests

# Build and cleanup configuration
target-path: "target"                      # Directory for compiled SQL and artifacts
clean-targets:                             # Directories to clean during 'dbt clean' command
    - "target"                             # Compiled SQL and run artifacts
    - "dbt_modules"                        # Legacy packages directory
    - "logs"                               # DBT execution logs

# DBT version compatibility requirements
require-dbt-version: [">=1.0.0", "<2.0.0"] # Ensure compatibility with DBT 1.x features

# Model configuration and materialization strategy
# This section defines how different model layers are materialized in Snowflake
models:
  F1_Data_Analysis:                         # Project name (must match 'name' above)
      materialized: table                   # Default materialization for all models

      # Staging layer configuration
      # Views are used for staging to minimize storage costs and enable real-time updates
      staging:
        materialized: view                  # Staging models as views for efficiency and freshness

      # Intermediate layer configuration (inherits default 'table' materialization)
      # Tables are used for intermediate models to improve query performance

      # Marts layer configuration (inherits default 'table' materialization)
      # Tables are used for marts to ensure fast dashboard and reporting performance
