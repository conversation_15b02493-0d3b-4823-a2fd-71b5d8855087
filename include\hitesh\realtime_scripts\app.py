"""
Formula 1 Real-Time Data Streaming Trigger Service

This Flask application serves as a bridge between Grafana dashboards and the real-time
F1 data streaming pipeline. It provides a web endpoint that can be called from Grafana
to trigger live data streaming for specific F1 sessions and drivers.

Architecture Overview:
- Receives HTTP requests from Grafana Cloud dashboard with session parameters
- Parses and validates the time range and session information
- Launches Kafka producer processes for car telemetry and location data
- Redirects the user to the real-time Grafana dashboard for live visualization

Key Features:
- RESTful API endpoint for triggering data streams
- Automatic time window calculation (5-minute windows for demo purposes)
- Parallel data streaming for car telemetry and location data
- Seamless integration with Grafana for real-time visualization
- Comprehensive error handling and logging

Use Case:
This service enables F1 race replay functionality where users can select any point
during a race session and watch the telemetry data stream in real-time, creating
an immersive race analysis experience.

Author: <PERSON><PERSON>
Created: 2024
"""

# Standard library imports for process management and date handling
import subprocess
from datetime import datetime, timedelta
import logging

# Flask framework for web service functionality
from flask import Flask, request, redirect

# Configure logging for production monitoring
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask application
app = Flask(__name__)


@app.route('/run-and-redirect', methods=['GET'])
def run_script():
    """
    Trigger real-time F1 data streaming and redirect to visualization dashboard.

    This endpoint receives parameters from Grafana dashboard interactions and initiates
    live data streaming for the specified time range and session. It launches parallel
    Kafka producers for different data types and redirects the user to the real-time
    dashboard for immediate visualization.

    Query Parameters:
        start (str): Start timestamp in ISO format (YYYY-MM-DDTHH:MM:SS)
        session (str): F1 session key for data filtering
        driver (str, optional): Driver number to focus on (defaults to '55')

    Returns:
        redirect: HTTP redirect to Grafana real-time dashboard
        dict: Error response with 500 status code if processing fails

    Example:
        GET /run-and-redirect?start=2024-03-10T14:30:00&session=9158&driver=44

    Note:
        This creates a 5-minute streaming window starting from the specified time.
        The service launches background processes that continue streaming data
        even after the HTTP response is returned.
    """
    try:
        # Extract and parse request parameters from Grafana dashboard
        raw_start = request.args.get('start')           # Start timestamp from user selection
        start_time = raw_start.split(' ')[0]            # Remove any timezone suffix
        session = request.args.get('session')           # F1 session identifier
        driver = request.args.get('driver', '55')       # Driver number (default: Carlos Sainz)

        # Parse the start time and calculate 5-minute streaming window
        start_dt = datetime.strptime(start_time, '%Y-%m-%dT%H:%M:%S')
        end_dt = start_dt + timedelta(minutes=5)        # 5-minute demo window

        # Format timestamps for Kafka producer with millisecond precision
        start_time = start_dt.strftime('%Y-%m-%dT%H:%M:%S.200')
        end_time = end_dt.strftime('%Y-%m-%dT%H:%M:%S.200')

        # Log streaming parameters for monitoring and debugging
        logger.info(f"Start time: {start_time}")
        logger.info(f"End time: {end_time}")
        logger.info(f"Session: {session}")
        logger.info(f"Driver: {driver}")

        # Configure Kafka producer command for car telemetry data
        # This streams real-time car data (speed, throttle, brake, DRS, etc.)
        cmd_car = [
            'python3',
            '/home/<USER>/kafka_producer_v6_cloud.py',  # Kafka producer script path
            '--param1', start_time,                      # Stream start time
            '--param2', end_time,                        # Stream end time
            '--session', session,                        # F1 session filter
            '--data-types', 'car',                       # Data type: car telemetry
            '--driver', driver                           # Driver filter
        ]

        # Configure Kafka producer command for location/position data
        # This streams real-time position data for track visualization
        cmd_location = [
            'python3',
            '/home/<USER>/kafka_producer_v6_cloud.py',  # Same producer script
            '--param1', start_time,                      # Stream start time
            '--param2', end_time,                        # Stream end time
            '--session', session,                        # F1 session filter
            '--data-types', 'location',                  # Data type: location/position
            '--driver', driver                           # Driver filter
        ]

        # Launch parallel Kafka producers as background processes
        # These will continue streaming data independently of this HTTP request
        logger.info(f"Running car command: {' '.join(cmd_car)}")
        subprocess.Popen(cmd_car)                       # Start car data streaming

        logger.info(f"Running location command: {' '.join(cmd_location)}")
        subprocess.Popen(cmd_location)                  # Start location data streaming

        # Construct Grafana dashboard URL with streaming parameters
        # This redirects the user to the real-time visualization dashboard
        redirect_url = f'http://13.60.222.132:3000/d/f1-telemetry/f1-real-time-telemetry?orgId=1&from={start_time.replace("T", " ")}&to={end_time.replace("T", " ")}&timezone=browser&var-driver={driver}&refresh=1s'

        return redirect(redirect_url)

    except Exception as e:
        # Log error and return HTTP 500 response for debugging
        logger.error(f"Error: {str(e)}")
        return {'error': str(e)}, 500


if __name__ == '__main__':
    """
    Start the Flask application server.

    The application runs on all network interfaces (0.0.0.0) on port 5000,
    making it accessible from Grafana Cloud and other external services.
    """
    logger.info("Starting Flask app on port 5000")
    app.run(host='0.0.0.0', port=5000)  # Listen on all interfaces for external access